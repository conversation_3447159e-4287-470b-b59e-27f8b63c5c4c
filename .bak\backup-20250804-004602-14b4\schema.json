{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Universal Game Entity Sc<PERSON>a", "description": "A comprehensive schema for representing all entity types in complex simulation games like CDDA, Dwarf Fortress, and Kenshi", "type": "object", "required": ["id", "name", "type"], "properties": {"id": {"type": "string", "description": "Unique identifier used internally by the game engine", "examples": ["human_male", "9mm_pistol", "forest_tile"]}, "name": {"type": "string", "description": "Display name visible to players"}, "type": {"type": "string", "description": "Primary classification of the entity", "enum": ["player", "npc", "enemy", "animal", "creature", "item", "terrain", "vehicle", "construction"]}, "description": {"type": "string", "description": "Flavor text describing the entity to players"}, "category": {"type": "string", "description": "Secondary classification for organizational purposes", "examples": ["weapon", "armor", "food", "monster", "resource"]}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Flexible tagging system for game mechanics and queries"}, "core_attributes": {"type": "object", "description": "Fundamental properties affecting all entities", "properties": {"weight": {"type": "number", "description": "Physical weight in game-appropriate units", "examples": [0.5, 2.3, 150.0]}, "volume": {"type": "number", "description": "Physical space occupied"}, "value": {"type": "integer", "description": "Economic value in game currency units"}, "material": {"type": "array", "items": {"type": "string"}, "description": "Composition materials", "examples": [["steel"], ["flesh", "bone"], ["wood", "iron"]]}, "symbol": {"type": "string", "description": "Character representation in ASCII displays", "maxLength": 1}, "color": {"type": "string", "description": "Display color in game's color system"}}}, "stats": {"type": "object", "description": "Numerical attributes and measurements", "properties": {"health": {"type": "object", "properties": {"current": {"type": "number"}, "max": {"type": "number"}, "regen_rate": {"type": "number"}}}, "stamina": {"type": "object", "properties": {"current": {"type": "number"}, "max": {"type": "number"}, "regen_rate": {"type": "number"}}}, "primary_attributes": {"type": "object", "description": "Core stats like strength, dexterity, etc.", "properties": {"str": {"type": "integer"}, "dex": {"type": "integer"}, "int": {"type": "integer"}, "per": {"type": "integer"}}}, "resistances": {"type": "object", "description": "Damage and effect resistances", "properties": {"physical": {"type": "number"}, "fire": {"type": "number"}, "cold": {"type": "number"}, "electric": {"type": "number"}, "poison": {"type": "number"}, "bionic": {"type": "number"}}}}}, "conditions": {"type": "array", "description": "Temporary states affecting the entity", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "duration": {"type": "integer", "description": "Turns remaining"}, "stacks": {"type": "integer", "default": 1}, "severity": {"type": "integer", "description": "0-5 scale of effect strength"}, "effects": {"type": "object", "description": "Stat modifications from this condition", "properties": {"health_mod": {"type": "number"}, "speed_mod": {"type": "number"}, "visibility_mod": {"type": "number"}}}}, "required": ["id", "name"]}}, "traits": {"type": "array", "description": "Permanent characteristics and features", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "positive": {"type": "boolean", "description": "Whether trait is generally beneficial"}, "effects": {"type": "object", "description": "Permanent stat modifications"}, "conflicts_with": {"type": "array", "items": {"type": "string"}, "description": "<PERSON><PERSON><PERSON> IDs this trait cannot coexist with"}}, "required": ["id", "name"]}}, "inventory": {"type": "object", "description": "Container for items carried or contained", "properties": {"capacity": {"type": "number", "description": "Maximum volume/weight capacity"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "quantity": {"type": "integer", "default": 1}, "condition": {"type": "number", "description": "0-1 scale of item integrity"}, "contained_in": {"type": "string", "description": "ID of container item"}}}}, "worn_items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "location": {"type": "string", "description": "Body part worn on"}}}}}}, "needs": {"type": "object", "description": "Biological/social requirements for living entities", "properties": {"hunger": {"type": "object", "properties": {"current": {"type": "number"}, "max": {"type": "number"}, "decay_rate": {"type": "number"}}}, "thirst": {"type": "object", "properties": {"current": {"type": "number"}, "max": {"type": "number"}, "decay_rate": {"type": "number"}}}, "sleep": {"type": "object", "properties": {"current": {"type": "number"}, "max": {"type": "number"}, "decay_rate": {"type": "number"}}}, "fun": {"type": "object", "description": "Mental health requirement", "properties": {"current": {"type": "number"}, "max": {"type": "number"}}}}}, "appearance": {"type": "object", "description": "Visual representation and customization options", "properties": {"sprite": {"type": "object", "properties": {"base": {"type": "string"}, "variants": {"type": "object", "additionalProperties": {"type": "string"}}}}, "color_scheme": {"type": "object", "description": "Customizable color regions", "additionalProperties": {"type": "string"}}, "body_type": {"type": "string", "description": "Anatomical structure template"}}}, "combat": {"type": "object", "description": "Combat-related properties", "properties": {"melee": {"type": "object", "properties": {"skill": {"type": "string"}, "damage": {"type": "object", "properties": {"amount": {"type": "number"}, "type": {"type": "string", "enum": ["cut", "stab", "bash", "acid", "fire"]}}}, "speed": {"type": "number", "description": "Attack speed factor"}}}, "ranged": {"type": "object", "properties": {"skill": {"type": "string"}, "ammo_types": {"type": "array", "items": {"type": "string"}}, "range": {"type": "number"}}}, "armor": {"type": "object", "description": "Protection values by body part", "properties": {"head": {"type": "number"}, "torso": {"type": "number"}, "arms": {"type": "number"}, "legs": {"type": "number"}}}}}, "ai": {"type": "object", "description": "Behavior and decision-making parameters", "properties": {"aggression": {"type": "number", "description": "0-10 scale of hostile tendency"}, "fear": {"type": "number", "description": "0-10 scale of risk aversion"}, "intelligence": {"type": "number", "description": "0-10 scale of tactical ability"}, "personality": {"type": "object", "properties": {"bravery": {"type": "number"}, "altruism": {"type": "number"}, "patience": {"type": "number"}}}, "goals": {"type": "array", "items": {"type": "string", "description": "Priority-ranked objectives"}}}}, "interaction": {"type": "object", "description": "How players and other entities can interact with this", "properties": {"actions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "required_skills": {"type": "object", "additionalProperties": {"type": "integer"}}, "time_cost": {"type": "number", "description": "In game time units"}}}}, "crafting": {"type": "object", "properties": {"recipes": {"type": "array", "items": {"type": "string", "description": "Recipe IDs this entity can be used in"}}, "can_craft": {"type": "boolean", "description": "Whether this entity can craft items"}}}}}, "terrain": {"type": "object", "description": "Properties relevant to terrain and environmental entities", "properties": {"movement_cost": {"type": "number", "description": "Relative difficulty to traverse"}, "temperature": {"type": "number", "description": "Environmental temperature effect"}, "flammability": {"type": "number", "description": "0-1 scale of fire susceptibility"}, "light_transmission": {"type": "number", "description": "0-1 scale of light passing through"}, "sound_transmission": {"type": "number", "description": "0-1 scale of sound passing through"}}}, "relations": {"type": "object", "description": "Social and factional relationships", "properties": {"faction": {"type": "string", "description": "Primary faction affiliation"}, "faction_rep": {"type": "object", "description": "Reputation with other factions", "additionalProperties": {"type": "number"}}, "allies": {"type": "array", "items": {"type": "string", "description": "IDs of allied entities"}}, "enemies": {"type": "array", "items": {"type": "string", "description": "IDs of enemy entities"}}, "relationships": {"type": "array", "items": {"type": "object", "properties": {"target_id": {"type": "string"}, "type": {"type": "string", "enum": ["friend", "rival", "lover", "family"]}, "strength": {"type": "number", "description": "0-1 scale of relationship intensity"}}}}}}, "customization": {"type": "object", "description": "Options for player customization of this entity", "properties": {"options": {"type": "array", "items": {"type": "object", "properties": {"category": {"type": "string", "description": "e.g., 'appearance', 'skills'"}, "id": {"type": "string"}, "name": {"type": "string"}, "choices": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "cost": {"type": "number", "description": "Customization points required"}}}}}}}, "current_selections": {"type": "object", "additionalProperties": {"type": "string"}}}}, "effects": {"type": "array", "description": "Passive or active game effects this entity provides", "items": {"type": "object", "properties": {"id": {"type": "string"}, "trigger": {"type": "string", "enum": ["always", "on_attack", "on_damage", "on_death", "on_use"]}, "effects": {"type": "object", "description": "Stat modifications or actions triggered", "properties": {"heal": {"type": "number"}, "damage": {"type": "number"}, "apply_condition": {"type": "string"}, "spawn_entity": {"type": "string"}}}, "cooldown": {"type": "number", "description": "Turns between effect triggers"}}}}, "version": {"type": "string", "description": "Schema version this entity conforms to"}, "metadata": {"type": "object", "description": "Editorial and development information", "properties": {"author": {"type": "string"}, "created": {"type": "string", "format": "date-time"}, "last_modified": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "description": "Developer notes for modders"}}}}, "examples": [{"id": "human_survivor", "name": "Survivor", "type": "npc", "description": "A regular human trying to survive the apocalypse", "stats": {"health": {"current": 100, "max": 100}, "primary_attributes": {"str": 8, "dex": 7, "int": 9, "per": 6}}, "traits": [{"id": "FASTHEALER", "name": "Fast Healer", "positive": true, "effects": {"health_mod": 0.2}}], "needs": {"hunger": {"current": 50, "max": 100, "decay_rate": 0.5}, "thirst": {"current": 60, "max": 100, "decay_rate": 0.8}}}, {"id": "9mm_pistol", "name": "9mm Pistol", "type": "item", "description": "A standard semi-automatic pistol", "core_attributes": {"weight": 1.2, "volume": 0.5, "material": ["steel", "plastic"]}, "combat": {"ranged": {"skill": "pistol", "ammo_types": ["9mm"], "range": 15}}}]}