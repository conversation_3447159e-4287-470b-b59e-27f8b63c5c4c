<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>World Editor</title>
  <script crossorigin src="https://unpkg.com/react@17/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
  <div id="root"></div>
  <script type="text/babel">
    const { useState, useEffect } = React;

    const App = () => {
      const [entityType, setEntityType] = useState("players");
      const [entities, setEntities] = useState([]);
      const [schema, setSchema] = useState(null);
      const [loading, setLoading] = useState(true);
      const [error, setError] = useState(null);

      useEffect(() => {
        // Test basic connectivity
        fetch('/schema')
          .then(res => {
            if (!res.ok) throw new Error(`HTTP ${res.status}`);
            return res.json();
          })
          .then(data => {
            setSchema(data);
            setLoading(false);
          })
          .catch(err => {
            setError(`Failed to load schema: ${err.message}`);
            setLoading(false);
          });
      }, []);

      useEffect(() => {
        if (!loading && !error) {
          fetch(`/${entityType}`)
            .then(res => {
              if (!res.ok) throw new Error(`HTTP ${res.status}`);
              return res.json();
            })
            .then(data => setEntities(data || []))
            .catch(err => {
              console.error('Failed to load entities:', err);
              setEntities([]);
            });
        }
      }, [entityType, loading, error]);

      if (loading) {
        return <div className="p-4">Loading...</div>;
      }

      if (error) {
        return <div className="p-4 text-red-600">Error: {error}</div>;
      }

      return (
        <div className="p-4">
          <h1 className="text-2xl font-bold mb-4">World Editor</h1>
          <p className="mb-4 text-green-600">✓ Connected to server successfully!</p>

          <select
            className="mb-4 p-2 border rounded"
            value={entityType}
            onChange={e => setEntityType(e.target.value)}
          >
            <option value="players">Players</option>
            <option value="items">Items</option>
            <option value="terrain">Terrain</option>
          </select>

          <div className="mt-4">
            <h2 className="text-xl font-semibold">Existing {entityType}</h2>
            <p className="text-sm text-gray-600 mb-2">Found {entities.length} entities</p>
            {entities.length === 0 ? (
              <p className="text-gray-500">No entities found. The JSON file might be empty.</p>
            ) : (
              <ul className="space-y-2">
                {entities.map((entity, index) => (
                  <li key={entity.id || index} className="p-2 border rounded bg-gray-50">
                    <strong>ID:</strong> {entity.id || 'No ID'}<br/>
                    <strong>Data:</strong> {JSON.stringify(entity.data || entity, null, 2)}
                  </li>
                ))}
              </ul>
            )}
          </div>

          <div className="mt-4 p-4 bg-blue-50 rounded">
            <h3 className="font-semibold">Schema Info</h3>
            <p>Schema loaded: {schema ? '✓ Yes' : '✗ No'}</p>
            {schema && <p>Schema title: {schema.title}</p>}
          </div>
        </div>
      );
    };

    ReactDOM.render(<App />, document.getElementById('root'));
  </script>
</body>
</html>