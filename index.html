<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>World Editor</title>
  <script src="https://cdn.jsdelivr.net/npm/react@17/umd/react.development.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom@17/umd/react-dom.development.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@rjsf/core@3.2.1/dist/react-jsonschema-form.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
  <div id="root"></div>
  <script type="text/babel">
    const { useState, useEffect } = React;
    const Form = window["react-jsonschema-form"].default;

    const App = () => {
      const [schema, setSchema] = useState({});
      const [entityType, setEntityType] = useState("players");
      const [entities, setEntities] = useState([]);
      const [formData, setFormData] = useState({});

      useEffect(() => {
        fetch('http://localhost:8080/schema')
          .then(res => res.json())
          .then(data => setSchema(data));
      }, []);

      useEffect(() => {
        fetch(`http://localhost:8080/${entityType}`)
          .then(res => res.json())
          .then(data => setEntities(data));
      }, [entityType]);

      const handleSubmit = ({ formData }) => {
        fetch(`http://localhost:8080/${entityType}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: formData.id || `new-${Date.now()}`, type: entityType, data: formData })
        })
          .then(res => res.json())
          .then(data => alert(data.message || data.error))
          .catch(err => alert('Error saving entity'));
      };

      return (
        <div className="p-4">
          <h1 className="text-2xl font-bold mb-4">World Editor</h1>
          <select
            className="mb-4 p-2 border rounded"
            value={entityType}
            onChange={e => setEntityType(e.target.value)}
          >
            <option value="players">Players</option>
            <option value="items">Items</option>
            <option value="terrain">Terrain</option>
          </select>
          <Form
            schema={schema}
            formData={formData}
            onChange={({ formData }) => setFormData(formData)}
            onSubmit={handleSubmit}
            className="max-w-2xl"
          />
          <div className="mt-4">
            <h2 className="text-xl font-semibold">Existing Entities</h2>
            <ul>
              {entities.map(entity => (
                <li key={entity.id} className="p-2 border-b">
                  {entity.id}: {JSON.stringify(entity.data)}
                  <button
                    className="ml-2 bg-blue-500 text-white p-1 rounded"
                    onClick={() => setFormData(entity.data)}
                  >
                    Edit
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      );
    };

    ReactDOM.render(<App />, document.getElementById('root'));
  </script>
</body>
</html>