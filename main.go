package main

import (
	"encoding/json"
	"net/http"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/xeipuuv/gojsonschema"
)

type Entity struct {
	ID   string                 `json:"id"`
	Type string                 `json:"type"`
	Data map[string]interface{} `json:"data"`
}

var schema []byte // Loaded schema file
var dataFiles = map[string]string{
	"players": "players.json",
	"items":   "items.json",
	"terrain": "terrain.json",
}

func main() {
	r := gin.Default()

	// Add CORS middleware
	r.Use(cors.Default())

	// Load schema
	var err error
	schema, err = os.ReadFile("schema.json")
	if err != nil {
		panic(err)
	}

	// Serve static files
	r.Static("/static", "./static")
	r.StaticFile("/", "./index.html")
	r.<PERSON>atic<PERSON>ile("/index.html", "./index.html")
	r.<PERSON>atic<PERSON>ile("/test.html", "./test.html")

	// Serve schema
	r.GET("/schema", func(c *gin.Context) {
		c.Data(http.StatusOK, "application/json", schema)
	})

	// Get all entities of a type
	r.GET("/:type", func(c *gin.Context) {
		entityType := c.Param("type")
		filePath, exists := dataFiles[entityType]
		if !exists {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entity type"})
			return
		}

		data, err := os.ReadFile(filePath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read data"})
			return
		}
		c.Data(http.StatusOK, "application/json", data)
	})

	// Save entity
	r.POST("/:type", func(c *gin.Context) {
		entityType := c.Param("type")
		filePath, exists := dataFiles[entityType]
		if !exists {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entity type"})
			return
		}

		var entity Entity
		if err := c.ShouldBindJSON(&entity); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Validate against schema
		schemaLoader := gojsonschema.NewStringLoader(string(schema))
		documentLoader := gojsonschema.NewGoLoader(entity.Data)
		result, err := gojsonschema.Validate(schemaLoader, documentLoader)
		if err != nil || !result.Valid() {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid data", "details": result.Errors()})
			return
		}

		// Load existing data
		var entities []Entity
		data, err := os.ReadFile(filePath)
		if err == nil {
			json.Unmarshal(data, &entities)
		}

		// Update or add entity
		for i, e := range entities {
			if e.ID == entity.ID {
				entities[i] = entity
				goto save
			}
		}
		entities = append(entities, entity)

	save:
		data, err = json.MarshalIndent(entities, "", "  ")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save data"})
			return
		}
		os.WriteFile(filePath, data, 0644)
		c.JSON(http.StatusOK, gin.H{"message": "Entity saved"})
	})

	r.Run(":8080")
}
