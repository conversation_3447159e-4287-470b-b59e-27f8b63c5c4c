<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Test Page</h1>
    <p>If you can see this, the server is working!</p>
    <script>
        console.log('JavaScript is working');
        fetch('/schema')
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Schema loaded:', data);
                document.body.innerHTML += '<p style="color: green;">✓ API is working! Schema loaded.</p>';
            })
            .catch(error => {
                console.error('Error:', error);
                document.body.innerHTML += '<p style="color: red;">✗ API error: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
